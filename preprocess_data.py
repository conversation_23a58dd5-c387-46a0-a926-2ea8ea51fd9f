# preprocess_data.py (Enhanced Preprocessing Implementation)

import argparse
import os
import torch
import numpy as np
import glob
from tqdm import tqdm

from environment import Environment
from gnn_model import prepare_node_features


def preprocess_and_save(raw_dataset_dir, processed_dataset_dir, sensing_radius, use_multiscale=False, apply_augmentation=True):
    """
    增强版数据预处理函数，支持特征归一化和数据增强
    """
    print("=" * 60)
    print("Starting Enhanced Data Preprocessing...")
    print(f"Source: '{raw_dataset_dir}' -> Destination: '{processed_dataset_dir}'")
    print(f"Using multiscale graph: {use_multiscale}, Applying augmentation: {apply_augmentation}")
    print("=" * 60)

    os.makedirs(processed_dataset_dir, exist_ok=True)

    file_paths = sorted(glob.glob(os.path.join(raw_dataset_dir, 'episode_*.pth')))
    if not file_paths:
        raise FileNotFoundError(f"No raw .pth files found in directory: '{raw_dataset_dir}'.")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 计算特征统计信息用于归一化
    all_features = []
    print("Computing feature statistics for normalization...")
    for f_path in tqdm(file_paths, desc="Computing Statistics"):  # 移除了[:min(100, len(file_paths))]限制
        data = torch.load(f_path, map_location='cpu', weights_only=False)
        trajectory = data['trajectory']
        
        env = Environment(
            peak_centers=data['peak_centers'],
            peak_std=data['peak_stds'],
            peak_amps=data['peak_amps'],
            device=device
        )
        
        for t in range(trajectory.shape[0] - 1):
            robot_pos_t = trajectory[t].to(device)
            node_features, _ = prepare_node_features(robot_pos_t, env, sensing_radius, use_multiscale=False)
            all_features.append(node_features.cpu())
    
    # 计算均值和标准差
    all_features = torch.cat(all_features, dim=0)
    feature_mean = all_features.mean(dim=0)
    feature_std = all_features.std(dim=0)
    
    # 保存归一化参数
    norm_params = {
        'mean': feature_mean,
        'std': feature_std
    }
    torch.save(norm_params, os.path.join(processed_dataset_dir, 'normalization_params.pth'))
    print(f"Normalization parameters saved: mean={feature_mean}, std={feature_std}")

    for f_path in tqdm(file_paths, desc="Preprocessing Episodes"):
        data = torch.load(f_path, map_location='cpu', weights_only=False)
        trajectory = data['trajectory']

        env = Environment(
            peak_centers=data['peak_centers'],
            peak_std=data['peak_stds'],
            peak_amps=data['peak_amps'],
            device=device
        )

        expert_actions = ((trajectory[1:] - trajectory[:-1]) / 0.05).to(device)

        node_features_seq, adj_matrices_seq = [], []
        for t in range(trajectory.shape[0] - 1):
            robot_pos_t = trajectory[t].to(device)
            node_features, adj_matrix = prepare_node_features(
                robot_pos_t, env, sensing_radius, 
                use_multiscale=use_multiscale, 
                normalize_params=norm_params
            )
            node_features_seq.append(node_features)
            adj_matrices_seq.append(adj_matrix)

        if not node_features_seq:
            continue
            
        # 数据增强：随机旋转
        if apply_augmentation and np.random.rand() < 0.5:  # 50%概率应用增强
            # 随机旋转
            theta = np.random.uniform(0, 2*np.pi)
            rot_matrix = torch.tensor([
                [np.cos(theta), -np.sin(theta)],
                [np.sin(theta), np.cos(theta)]
            ], dtype=torch.float32, device=device)  # 明确指定float32类型

            # 应用旋转到位置差异特征和专家动作
            for i in range(len(node_features_seq)):
                # 旋转位置差异 (前两维)
                pos_diff = node_features_seq[i][:, :2]
                node_features_seq[i][:, :2] = torch.matmul(pos_diff, rot_matrix.T)

                # 旋转专家动作
                expert_actions[i] = torch.matmul(expert_actions[i], rot_matrix.T)

        processed_data = {
            'node_features': torch.stack(node_features_seq).cpu(),
            'adj_matrices': torch.stack(adj_matrices_seq).cpu(),
            'expert_actions': expert_actions.cpu()
        }

        save_path = os.path.join(processed_dataset_dir, os.path.basename(f_path))
        torch.save(processed_data, save_path)

    print("\nPreprocessing complete.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Preprocess raw trajectory data for GCRN training.')
    parser.add_argument('--raw_dir', type=str, default='training_dataset_raw')
    parser.add_argument('--processed_dir', type=str, default='training_dataset_processed')
    parser.add_argument('--sensing_radius', type=float, default=5.0)
    parser.add_argument('--use_multiscale', action='store_true', help='Use multiscale graph construction')
    parser.add_argument('--apply_augmentation', action='store_true', help='Apply data augmentation')
    args = parser.parse_args()
    preprocess_and_save(args.raw_dir, args.processed_dir, args.sensing_radius, 
                        args.use_multiscale, args.apply_augmentation)
