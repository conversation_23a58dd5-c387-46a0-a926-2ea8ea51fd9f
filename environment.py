# environment.py

import torch
import numpy as np

class Environment:
    """
    [FINAL & COMPLETE]
    Defines the simulation environment with a Gaussian-mixture density field.
    Contains all necessary helper functions to prevent ImportErrors.
    """
    def __init__(self, width=40, height=40, n_peaks=3, grid_resolution=1.0, device=None, peak_centers=None, peak_std=None, peak_amps=None):
        self.width = width
        self.height = height
        self.n_peaks = n_peaks
        self.grid_resolution = grid_resolution
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu') if device is None else device

        # Generate grid points for integration and reward calculation
        self.xx, self.yy = np.meshgrid(
            np.arange(self.grid_resolution / 2, self.width, self.grid_resolution, dtype=np.float32),
            np.arange(self.grid_resolution / 2, self.height, self.grid_resolution, dtype=np.float32)
        )
        self.grid_points = np.stack([self.xx.ravel(), self.yy.ravel()]).T
        self.grid_points_torch = torch.from_numpy(self.grid_points).float().to(self.device)

        if peak_centers is None and peak_std is None:
            # Generate peak parameters randomly if not provided
            scale = 0.8
            margin_x, margin_y = self.width * (1 - scale) / 2, self.height * (1 - scale) / 2
            peak_x = margin_x + np.random.rand(self.n_peaks) * (self.width * scale)
            peak_y = margin_y + np.random.rand(self.n_peaks) * (self.height * scale)
            self.peak_centers_np = np.stack([peak_x, peak_y], axis=1).astype(np.float32)

            min_std, max_std = 4.0, 6.0 # Constrained sigma range [4, 6]
            self.stds_np = min_std + np.random.rand(self.n_peaks) * (max_std - min_std)
            self.peak_std_np = np.stack([self.stds_np, self.stds_np], axis=1).astype(np.float32)
            self.peak_amps_np = np.ones(self.n_peaks, dtype=np.float32) * 2.0
        else:
            # Use provided parameters (for recreating envs during training data loading)
            self.peak_centers_np, self.stds_np, self.peak_amps_np = peak_centers, peak_std, peak_amps
            self.peak_std_np = np.stack([self.stds_np, self.stds_np], axis=1).astype(np.float32)

        # Transfer parameters to device
        self.peak_centers = torch.from_numpy(self.peak_centers_np).to(self.device)
        self.peak_std = torch.from_numpy(self.peak_std_np).to(self.device)
        self.peak_amps = torch.from_numpy(self.peak_amps_np).to(self.device)

        # Calculate the density field
        density_np = self._calculate_density_numpy(self.grid_points)
        self.density_field_torch = torch.from_numpy(density_np).float().to(self.device)

    def _calculate_density_numpy(self, points_np):
        density = np.zeros(points_np.shape[0], dtype=np.float32)
        for i in range(self.n_peaks):
            diff = points_np - self.peak_centers_np[i]
            exponent = -np.sum((diff ** 2) / (2 * self.peak_std_np[i] ** 2), axis=1)
            density += self.peak_amps_np[i] * np.exp(exponent)
        return density

    def calculate_coverage_reward(self, robot_pos):
        robot_pos = robot_pos.to(self.device)
        dist_to_robots = torch.cdist(self.grid_points_torch, robot_pos)
        voronoi_indices = torch.argmin(dist_to_robots, dim=1)
        total_reward = 0.0
        for i in range(robot_pos.shape[0]):
            my_mask = (voronoi_indices == i)
            if torch.any(my_mask):
                distances_sq = torch.sum((self.grid_points_torch[my_mask] - robot_pos[i]) ** 2, dim=1)
                reward_i = -torch.sum(distances_sq * self.density_field_torch[my_mask])
                total_reward += reward_i.item()
        return total_reward * 0.001 # Scaled by 1/1000

    def count_covered_peaks(self, robot_pos, sensing_radius):
        robot_pos = robot_pos.to(self.device)
        covered_peaks = 0
        for peak_center in self.peak_centers:
            distances_to_peak = torch.norm(robot_pos - peak_center, dim=1)
            if torch.any(distances_to_peak <= sensing_radius):
                covered_peaks += 1
        return covered_peaks

# --- HELPER FUNCTIONS ---
def get_voronoi_metrics(robot_pos, env, sensing_radius):
    device = env.device
    N = robot_pos.shape[0]
    robot_pos = robot_pos.to(device)
    dist_sq = torch.cdist(env.grid_points_torch, robot_pos).pow(2)
    voronoi_indices = torch.argmin(dist_sq, dim=1)
    masses = torch.zeros(N, device=device)
    centroids = torch.zeros(N, 2, device=device)
    for i in range(N):
        my_voronoi_mask = (voronoi_indices == i)
        dist_to_robot = torch.norm(env.grid_points_torch - robot_pos[i], dim=1)
        in_sensing_range_mask = (dist_to_robot <= sensing_radius)
        integration_mask = my_voronoi_mask & in_sensing_range_mask
        if torch.any(integration_mask):
            phi_values = env.density_field_torch[integration_mask]
            sum_of_densities = torch.sum(phi_values)
            masses[i] = sum_of_densities * (env.grid_resolution ** 2)
            if sum_of_densities > 1e-9:
                centroids[i] = torch.sum(env.grid_points_torch[integration_mask] * phi_values.unsqueeze(1), dim=0) / sum_of_densities
            else:
                centroids[i] = robot_pos[i]
        else:
            masses[i] = 1e-9
            centroids[i] = robot_pos[i]
    return masses, centroids

def get_voronoi_metrics_infinite(robot_pos, env):
    device = env.device
    N = robot_pos.shape[0]
    robot_pos = robot_pos.to(device)
    dist_sq = torch.cdist(env.grid_points_torch, robot_pos).pow(2)
    voronoi_indices = torch.argmin(dist_sq, dim=1)
    centroids = torch.zeros(N, 2, device=device)
    for i in range(N):
        my_voronoi_mask = (voronoi_indices == i)
        if torch.any(my_voronoi_mask):
            phi_values = env.density_field_torch[my_voronoi_mask]
            sum_of_densities = torch.sum(phi_values)
            if sum_of_densities > 1e-9:
                centroids[i] = torch.matmul(env.grid_points_torch[my_voronoi_mask].t(), phi_values) / sum_of_densities
            else:
                centroids[i] = robot_pos[i]
        else:
            centroids[i] = robot_pos[i]
    return None, centroids

def lloyd_algorithm_step(robot_pos, env, sensing_radius=None, infinite_sensing=False):
    if not infinite_sensing:
        masses, centroids = get_voronoi_metrics(robot_pos, env, sensing_radius)
        control_inputs = 2 * masses.unsqueeze(1) * (centroids - robot_pos.to(env.device))
    else:
        _, centroids = get_voronoi_metrics_infinite(robot_pos, env)
        control_inputs = (centroids - robot_pos.to(env.device)) * 0.5
    return control_inputs