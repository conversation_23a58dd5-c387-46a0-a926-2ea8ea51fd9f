# evaluation.py (Final Definitive Version)

import torch
import numpy as np
import matplotlib

matplotlib.use('Agg')
import matplotlib.pyplot as plt
import os
import inspect
from tqdm import tqdm

from gnn_model import GCRNPolicy, prepare_node_features, LloydPolicy


def plot_and_save_episode(trajectory, env, output_dir, file_name_base, title, final_reward=None):
    """ (最终版) 绘制并保存一个回合的静态轨迹图。 """
    N_ROBOTS = trajectory[0].shape[0]
    plt.figure(figsize=(12, 12))

    peak_centers = env.peak_centers.cpu().numpy()
    plt.scatter(
        peak_centers[:, 0], peak_centers[:, 1],
        marker='s', s=120, c='red', label='Density Centers', zorder=5
    )

    trajectory_np = torch.stack(trajectory).cpu().numpy()
    for i in range(N_ROBOTS):
        plt.plot(trajectory_np[:, i, 0], trajectory_np[:, i, 1], alpha=0.4, linewidth=1.5)

    plt.scatter(
        trajectory_np[0, :, 0], trajectory_np[0, :, 1],
        marker='o', s=60, c='blue', label='Start Positions', zorder=4
    )
    plt.scatter(
        trajectory_np[-1, :, 0], trajectory_np[-1, :, 1],
        marker='*', s=200, c='green', label='End Positions', zorder=6, edgecolors='black'
    )

    peak_info_str = "Peaks (Center | Std Dev):\n" + "\n".join(
        [f"({c[0]:.1f}, {c[1]:.1f} | σ={s:.2f})" for c, s in zip(env.peak_centers_np, env.stds_np)]
    )

    plot_title = title
    if final_reward is not None:
        plot_title += f'\nFinal Reward: {final_reward:.2f}'
    plt.title(plot_title, fontsize=14, weight='bold')

    plt.figtext(
        0.5, 0.01, peak_info_str, ha="center", va="bottom", fontsize=10,
        bbox={"facecolor": "white", "alpha": 0.7, "pad": 5}
    )

    plt.xlabel('X');
    plt.ylabel('Y')
    plt.legend(bbox_to_anchor=(1.04, 1), loc='upper left')
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.axis('equal');
    plt.xlim(0, env.width);
    plt.ylim(0, env.height)
    plt.subplots_adjust(right=0.75, bottom=0.15)

    plot_path = os.path.join(output_dir, f"{file_name_base}.png")
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()


class CoverageEvaluator:
    def __init__(self, env, sensing_radius, device=None):
        self.env = env
        self.sensing_radius = sensing_radius
        self.device = device or torch.device('cpu')

    def run_episode(self, policy, initial_pos, max_steps):
        """ [最终版] 只运行一个固定步长的回合。 """
        if isinstance(policy, torch.nn.Module):
            policy.eval().to(self.device)

        if hasattr(policy, 'reset'):
            reset_spec = inspect.getfullargspec(policy.reset)
            if len(reset_spec.args) > 1:
                policy.reset(initial_pos.shape[0], self.device)
            else:
                policy.reset()

        robot_pos = initial_pos.clone().to(self.device)
        trajectory = [robot_pos.clone().cpu()]
        rewards, peak_coverage = [], []

        for _ in range(max_steps):
            rewards.append(self.env.calculate_coverage_reward(robot_pos))
            peak_coverage.append(self.env.count_covered_peaks(robot_pos, self.sensing_radius))

            with torch.no_grad():
                control_action = policy(robot_pos, self.env)

            robot_pos += control_action * 0.05
            robot_pos[:, 0].clamp_(0, self.env.width)
            robot_pos[:, 1].clamp_(0, self.env.height)
            trajectory.append(robot_pos.clone().cpu())

        rewards.append(self.env.calculate_coverage_reward(robot_pos))

        return trajectory, rewards, peak_coverage