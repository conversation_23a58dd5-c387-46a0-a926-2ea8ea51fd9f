# expert_controller.py (Final Definitive Version)

import torch
import numpy as np
from environment import get_voronoi_metrics_infinite


class ExpertController:
    """
    [最终版]
    本专家控制器完美地结合了比例控制（源于您的Matlab逻辑）和最大速度限制，
    以生成高质量、平滑且高效的专家轨迹。
    """

    def __init__(self, control_gain=0.3, max_move_dist=0.5):
        """
        初始化专家控制器。

        Args:
            control_gain (float): 比例增益 (K_prop)，决定了朝向质心移动的“果断程度”。
            max_move_dist (float): 机器人在单步中允许移动的最大距离，作为速度上限。
        """
        self.K_prop = control_gain
        self.max_move_dist = max_move_dist

    def __call__(self, robot_pos, env):
        return self.get_expert_action(robot_pos, env)

    def reset(self):
        """为保持API一致性而保留，当前无需重置任何状态。"""
        pass

    def get_expert_action(self, current_pos, env):
        """
        基于“比例控制+速度上限”的混合策略，计算专家的控制动作。
        """
        # 1. 从环境中获取全局的Voronoi质心
        _, centroids = get_voronoi_metrics_infinite(current_pos, env)

        # 2. 根据比例控制，计算出期望的位置变化量
        desired_change_in_pos = self.K_prop * (centroids - current_pos)

        # 3. 对期望的位移强制施加最大距离限制
        action_norms = torch.norm(desired_change_in_pos, p=2, dim=1, keepdim=True)
        exceed_mask = action_norms > self.max_move_dist

        # 对超出的位移进行缩放
        capped_change = desired_change_in_pos * (self.max_move_dist / (action_norms + 1e-9))

        # 使用torch.where，应用速度上限
        final_change_in_pos = torch.where(exceed_mask, capped_change, desired_change_in_pos)

        # 4. 将最终的期望位移，转换为一个速度向量（即控制动作）
        dt = 0.05
        final_action = final_change_in_pos / dt

        return final_action


def generate_clustered_initial_positions(n_robots, env, n_clusters=3, cluster_std=5.0, min_dist=2.0, device=None):
    """ (最终确认版) 生成机器人的簇状初始位置。 """
    if device is None:
        device = env.device

    scale = 0.8
    margin_x, margin_y = env.width * (1 - scale) / 2, env.height * (1 - scale) / 2
    x_min, x_max = margin_x, env.width - margin_x
    y_min, y_max = margin_y, env.height - margin_y

    for _ in range(200):
        effective_width, effective_height = x_max - x_min, y_max - y_min
        cluster_centers = torch.stack([
            x_min + torch.rand(n_clusters, device=device) * effective_width,
            y_min + torch.rand(n_clusters, device=device) * effective_height
        ], dim=1)

        cluster_assignments = torch.randint(0, n_clusters, (n_robots,), device=device)
        positions = torch.zeros(n_robots, 2, device=device)

        for i in range(n_robots):
            positions[i] = cluster_centers[cluster_assignments[i]] + torch.randn(2, device=device) * cluster_std

        positions[:, 0].clamp_(min=x_min, max=x_max)
        positions[:, 1].clamp_(min=y_min, max=y_max)

        if n_robots <= 1:
            return positions
        if torch.min(torch.cdist(positions, positions).fill_diagonal_(float('inf'))) >= min_dist:
            return positions

    return positions